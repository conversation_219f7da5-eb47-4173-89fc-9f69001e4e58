import ccxt.async_support as ccxt_async
import asyncio
import time
from collections import defaultdict

def calculate_average_price_and_liquidity(orderbook_side, target_usd_amount, side_type='ask'):
    """
    Calculate average price and available liquidity for a target USD amount

    Args:
        orderbook_side: List of [price, amount] pairs from orderbook (bids or asks)
        target_usd_amount: Target amount in USD to trade
        side_type: 'ask' for buying, 'bid' for selling

    Returns:
        dict with average_price, max_usd_amount, slippage_percent, levels_used
    """
    if not orderbook_side or len(orderbook_side) == 0:
        return {
            'average_price': None,
            'max_usd_amount': 0,
            'slippage_percent': 0,
            'levels_used': 0,
            'achievable_usd': 0
        }

    total_usd = 0
    total_tokens = 0
    levels_used = 0

    best_price = orderbook_side[0][0]  # First level price for slippage calculation

    for price, amount in orderbook_side:
        levels_used += 1
        level_usd = price * amount

        if total_usd + level_usd <= target_usd_amount:
            # Can use this entire level
            total_usd += level_usd
            total_tokens += amount
        else:
            # Partial fill of this level to reach target
            remaining_usd = target_usd_amount - total_usd
            partial_tokens = remaining_usd / price
            total_usd += remaining_usd
            total_tokens += partial_tokens
            break

    if total_tokens == 0:
        return {
            'average_price': None,
            'max_usd_amount': 0,
            'slippage_percent': 0,
            'levels_used': 0,
            'achievable_usd': 0
        }

    average_price = total_usd / total_tokens

    # Calculate slippage
    if side_type == 'ask':  # Buying - slippage is how much higher we pay vs best ask
        slippage_percent = ((average_price - best_price) / best_price) * 100
    else:  # Selling - slippage is how much lower we get vs best bid
        slippage_percent = ((best_price - average_price) / best_price) * 100

    # Calculate maximum USD amount available (sum all levels)
    max_usd_amount = sum(price * amount for price, amount in orderbook_side)

    return {
        'average_price': average_price,
        'max_usd_amount': max_usd_amount,
        'slippage_percent': slippage_percent,
        'levels_used': levels_used,
        'achievable_usd': total_usd
    }

#rate limits
#    "ascendex",

# ALL exchanges as specified (will be filtered for availability)
DESIRED_EXCHANGES = [
    "bigone",
    "binance",
    "bitmart",
    "bitmex",
    "bitrue",
    "bitteam",
    "blockchaincom",
    "btcalpha",
    "btcturk",
    "cex",
    "coinbase",
    "coincatch",
    "coinsph",
    "cryptocom",
    "cryptomus",
    "digifinex",
    "hashkey",
    "hollaex",
    "kraken",
    "latoken",
    "lbank",
    "mexc",
    "myokx",
    "novadax",
    "oceanex",
    "p2b",
    "phemex",
    "poloniex",
    "probit",
    "tokocrypto",
    "tradeogre",
    "woo",
    "xt",
    "yobit"
]

def get_available_exchanges():
    """Get list of exchanges that are actually available in CCXT"""
    available = []
    unavailable = []

    print("🔍 Checking exchange availability in CCXT...")

    for exchange_name in DESIRED_EXCHANGES:
        if hasattr(ccxt_async, exchange_name):
            available.append(exchange_name)
        else:
            unavailable.append(exchange_name)

    print(f"✅ Available exchanges: {len(available)}/{len(DESIRED_EXCHANGES)}")
    if unavailable:
        print(f"❌ Unavailable exchanges ({len(unavailable)}): {', '.join(unavailable)}")
        print("   These exchanges are not available in your CCXT version")

    return available

def show_all_ccxt_exchanges():
    """Show all exchanges available in CCXT for reference"""
    print("\n📋 All exchanges available in your CCXT version:")
    all_exchanges = [name for name in dir(ccxt_async)
                    if not name.startswith('_') and hasattr(getattr(ccxt_async, name), '__call__')]
    # Filter to actual exchange classes (they should be callable and not utility functions)
    exchange_classes = []
    for name in all_exchanges:
        try:
            cls = getattr(ccxt_async, name)
            if hasattr(cls, 'id') or 'Exchange' in str(cls):
                exchange_classes.append(name)
        except:
            pass

    print(f"Total available: {len(exchange_classes)}")
    for i, ex in enumerate(sorted(exchange_classes), 1):
        if i % 6 == 0:
            print(f"{ex}")
        else:
            print(f"{ex:<15}", end=" ")
    print()

# This will be populated with actually available exchanges
SMART_EXCHANGES = []

# Exchange pool and market data
exchange_pool = {}
exchange_markets = {}

async def init_exchange_with_markets(exchange_name):
    """Initialize exchange and load its markets"""
    try:
        # Check if exchange exists in CCXT first
        if not hasattr(ccxt_async, exchange_name):
            print(f"✗ {exchange_name}: not available in CCXT version")
            return False

        exchange_class = getattr(ccxt_async, exchange_name)
        exchange = exchange_class({
            'enableRateLimit': False,
            'timeout': 5000,
            'options': {'defaultType': 'spot'},
        })

        # Load markets to know which tokens exist
        markets = await exchange.load_markets()

        # Extract USDT pairs
        usdt_tokens = set()
        for symbol in markets:
            if '/USDT' in symbol and markets[symbol]['active']:
                token = symbol.split('/')[0]
                usdt_tokens.add(token)

        exchange_pool[exchange_name] = exchange
        exchange_markets[exchange_name] = usdt_tokens

        print(f"✓ {exchange_name}: {len(usdt_tokens)} USDT tokens")
        return True

    except Exception as e:
        print(f"✗ {exchange_name}: {e}")
        return False

async def init_smart_exchanges():
    """Initialize exchanges and build smart token mapping"""
    global SMART_EXCHANGES

    print("🧠 Smart Exchange Initialization...")

    # First, get available exchanges
    SMART_EXCHANGES = get_available_exchanges()

    if not SMART_EXCHANGES:
        print("❌ No exchanges available in CCXT!")
        return {}

    tasks = [init_exchange_with_markets(ex) for ex in SMART_EXCHANGES]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    successful = sum(1 for r in results if r is True)
    print(f"📊 {successful}/{len(SMART_EXCHANGES)} exchanges ready")

    # Build smart token-to-exchange mapping
    token_exchange_map = defaultdict(list)
    for exchange_name, tokens in exchange_markets.items():
        for token in tokens:
            token_exchange_map[token].append(exchange_name)

    # Filter to tokens on multiple exchanges
    multi_exchange_tokens = {
        token: exchanges for token, exchanges in token_exchange_map.items()
        if len(exchanges) >= 2
    }

    print(f"🎯 Found {len(multi_exchange_tokens)} tokens on multiple exchanges")

    return multi_exchange_tokens

async def fetch_smart(exchange_name, symbol, semaphore, target_usd_amount=10.0):
    """Smart fetch with liquidity analysis - only called for tokens that exist on the exchange"""
    async with semaphore:
        try:
            exchange = exchange_pool[exchange_name]

            # Try ticker first (fastest) - but we'll still need orderbook for liquidity
            ticker_data = None
            try:
                ticker = await asyncio.wait_for(exchange.fetch_ticker(symbol), timeout=3.0)
                if ticker.get('bid') and ticker.get('ask'):
                    ticker_data = {
                        'bid': ticker['bid'],
                        'ask': ticker['ask']
                    }
            except:
                pass

            # Always try to get orderbook for liquidity analysis
            try:
                # Use higher limit for better liquidity analysis
                config = EXCHANGE_CONFIGS.get(exchange_name, {})
                limit = max(config.get('orderbook_limit', 10), 10)  # At least 10 levels
                orderbook = await asyncio.wait_for(exchange.fetch_order_book(symbol, limit=limit), timeout=4.0)

                if orderbook.get('bids') and orderbook.get('asks'):
                    # Calculate liquidity metrics
                    ask_liquidity = calculate_average_price_and_liquidity(
                        orderbook['asks'], target_usd_amount, 'ask'
                    )
                    bid_liquidity = calculate_average_price_and_liquidity(
                        orderbook['bids'], target_usd_amount, 'bid'
                    )

                    # Use liquidity-aware prices if available, otherwise fall back to top prices
                    buy_price = ask_liquidity['average_price'] if ask_liquidity['average_price'] else orderbook['asks'][0][0]
                    sell_price = bid_liquidity['average_price'] if bid_liquidity['average_price'] else orderbook['bids'][0][0]

                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': sell_price, 'ask': buy_price,
                        'top_bid': orderbook['bids'][0][0], 'top_ask': orderbook['asks'][0][0],
                        'ask_liquidity': ask_liquidity,
                        'bid_liquidity': bid_liquidity,
                        'orderbook_asks': orderbook['asks'],
                        'orderbook_bids': orderbook['bids'],
                        'timestamp': time.time(), 'error': None, 'method': 'orderbook_liquidity'
                    }
            except Exception as e:
                # If orderbook fails but we have ticker data, use that
                if ticker_data:
                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': ticker_data['bid'], 'ask': ticker_data['ask'],
                        'top_bid': ticker_data['bid'], 'top_ask': ticker_data['ask'],
                        'ask_liquidity': None, 'bid_liquidity': None,
                        'timestamp': time.time(), 'error': None, 'method': 'ticker_fallback'
                    }
                else:
                    return {
                        'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                        'timestamp': time.time(), 'error': str(e)[:100], 'method': 'failed'
                    }

        except Exception as e:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(e)[:100], 'method': 'failed'
            }

        # Fallback return (should never reach here)
        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(), 'error': 'No data available', 'method': 'no_data'
        }

# Exchange-specific configurations for problematic exchanges
EXCHANGE_CONFIGS = {
    'bitmex': {'timeout_multiplier': 3.0, 'max_retries': 4, 'delay': 2.0, 'prefer_orderbook': True},
    'myokx': {'timeout_multiplier': 3.0, 'max_retries': 2, 'delay': 3.0, 'prefer_ticker': True, 'low_concurrency': True},
    'coincatch': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'digifinex': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'woo': {'timeout_multiplier': 2.5, 'max_retries': 3, 'delay': 1.5, 'prefer_orderbook': True},
    'btcalpha': {'timeout_multiplier': 3.0, 'max_retries': 4, 'delay': 2.0, 'prefer_orderbook': True},
    'hashkey': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'binance': {'timeout_multiplier': 1.5, 'max_retries': 3, 'delay': 0.5, 'prefer_ticker': True, 'orderbook_limit': 5},
}

# Known problematic token-exchange combinations (to reduce "market not found" errors)
TOKEN_EXCHANGE_BLACKLIST = {

}

def is_token_blacklisted(exchange_name, token):
    """Check if a token is blacklisted for a specific exchange"""
    blacklist = TOKEN_EXCHANGE_BLACKLIST.get(exchange_name, set())
    return token in blacklist

async def retry_failed_requests_multi_round(failed_requests, max_concurrent=30, max_rounds=1):
    """Enhanced retry with multiple rounds and exchange-specific strategies"""
    print(f"🔄 Enhanced retry strategy: {max_rounds} rounds, {max_concurrent} concurrent requests")

    all_retry_results = []
    remaining_requests = failed_requests.copy()

    for round_num in range(1, max_rounds + 1):
        if not remaining_requests:
            break

        print(f"\n🔄 Retry Round {round_num}: {len(remaining_requests)} requests")

        # Adjust strategy per round
        round_concurrent = max(10, max_concurrent // round_num)  # Reduce concurrency each round
        base_delay = round_num * 0.5  # Increase delay each round

        semaphore = asyncio.Semaphore(round_concurrent)
        retry_tasks = []

        for exchange_name, symbol in remaining_requests:
            task = fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, round_num, base_delay, 10.0)
            retry_tasks.append(task)

        # Execute retries for this round
        round_results = []
        completed = 0
        total_retries = len(retry_tasks)

        print(f"🔄 Round {round_num} progress: ", end="", flush=True)

        for coro in asyncio.as_completed(retry_tasks):
            try:
                result = await coro
                if result is not None:
                    round_results.append(result)
                completed += 1

                if completed % max(1, total_retries // 10) == 0:
                    progress = (completed / total_retries) * 100
                    print(f"{progress:.0f}%", end=" ", flush=True)
            except Exception as e:
                completed += 1
                round_results.append({
                    'exchange': 'unknown', 'symbol': 'unknown', 'bid': None, 'ask': None,
                    'timestamp': time.time(), 'error': f"Round {round_num} exception: {str(e)[:30]}",
                    'method': f'retry_round_{round_num}_failed'
                })

        print()

        # Count successful retries for this round
        successful_round = [r for r in round_results
                           if r.get('error') is None and r.get('bid') and r.get('ask')]

        print(f"✅ Round {round_num} completed: {len(successful_round)}/{total_retries} successful")

        all_retry_results.extend(round_results)

        # Update remaining requests (remove successful ones)
        successful_keys = {(r['exchange'], r['symbol']) for r in successful_round}
        remaining_requests = [(ex, sym) for ex, sym in remaining_requests
                             if (ex, sym) not in successful_keys]

        if remaining_requests:
            print(f"🔄 {len(remaining_requests)} requests still need retry")
            # Add progressive delay between rounds
            if round_num < max_rounds:
                await asyncio.sleep(1.0 * round_num)

    # Final summary
    total_successful = len([r for r in all_retry_results
                           if r.get('error') is None and r.get('bid') and r.get('ask')])

    print(f"✅ Multi-round retry completed: {total_successful}/{len(failed_requests)} total successful")

    return all_retry_results


async def fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, round_num, base_delay, target_usd_amount=10.0):
    """Enhanced retry with exchange-specific strategies and progressive timeouts"""
    async with semaphore:
        # Add progressive delay before attempting
        if base_delay > 0:
            await asyncio.sleep(base_delay)

        exchange = exchange_pool.get(exchange_name)
        if not exchange:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': 'Exchange not available',
                'method': f'retry_round_{round_num}_unavailable'
            }

        # Get exchange-specific configuration
        config = EXCHANGE_CONFIGS.get(exchange_name, {
            'timeout_multiplier': 1.5, 'max_retries': 2, 'delay': 0.5, 'prefer_orderbook': False
        })

        # Calculate timeouts based on round and exchange config
        base_timeout = 3.0 + (round_num * 1.0)  # Increase timeout each round
        ticker_timeout = base_timeout * config['timeout_multiplier']
        orderbook_timeout = (base_timeout + 1.0) * config['timeout_multiplier']

        # Strategy selection based on exchange preference and round
        strategies = []
        if config.get('prefer_orderbook') or round_num > 1:
            strategies = ['orderbook', 'ticker']
        else:
            strategies = ['ticker', 'orderbook']

        last_error = None
        ticker_data = None

        for strategy in strategies:
            try:
                if strategy == 'ticker':
                    ticker = await asyncio.wait_for(
                        exchange.fetch_ticker(symbol),
                        timeout=ticker_timeout
                    )
                    if ticker.get('bid') and ticker.get('ask'):
                        ticker_data = {
                            'bid': ticker['bid'],
                            'ask': ticker['ask']
                        }
                        # Don't return immediately, try to get orderbook for liquidity

                elif strategy == 'orderbook':
                    # Use higher limit for better liquidity analysis
                    limit = max(config.get('orderbook_limit', 10), 10)
                    orderbook = await asyncio.wait_for(
                        exchange.fetch_order_book(symbol, limit=limit),
                        timeout=orderbook_timeout
                    )
                    if orderbook.get('bids') and orderbook.get('asks'):
                        # Calculate liquidity metrics
                        ask_liquidity = calculate_average_price_and_liquidity(
                            orderbook['asks'], target_usd_amount, 'ask'
                        )
                        bid_liquidity = calculate_average_price_and_liquidity(
                            orderbook['bids'], target_usd_amount, 'bid'
                        )

                        # Use liquidity-aware prices if available
                        buy_price = ask_liquidity['average_price'] if ask_liquidity['average_price'] else orderbook['asks'][0][0]
                        sell_price = bid_liquidity['average_price'] if bid_liquidity['average_price'] else orderbook['bids'][0][0]

                        return {
                            'exchange': exchange_name, 'symbol': symbol,
                            'bid': sell_price, 'ask': buy_price,
                            'top_bid': orderbook['bids'][0][0], 'top_ask': orderbook['asks'][0][0],
                            'ask_liquidity': ask_liquidity,
                            'bid_liquidity': bid_liquidity,
                            'orderbook_asks': orderbook['asks'],
                            'orderbook_bids': orderbook['bids'],
                            'timestamp': time.time(), 'error': None,
                            'method': f'retry_round_{round_num}_orderbook_liquidity'
                        }

            except Exception as e:
                last_error = str(e)[:80]  # Capture more error detail
                continue  # Try next strategy

        # If we have ticker data but orderbook failed, use ticker
        if ticker_data:
            return {
                'exchange': exchange_name, 'symbol': symbol,
                'bid': ticker_data['bid'], 'ask': ticker_data['ask'],
                'top_bid': ticker_data['bid'], 'top_ask': ticker_data['ask'],
                'ask_liquidity': None, 'bid_liquidity': None,
                'timestamp': time.time(), 'error': None,
                'method': f'retry_round_{round_num}_ticker_fallback'
            }

        # All strategies failed
        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(),
            'error': f"Round {round_num} failed: {last_error}" if last_error else f"Round {round_num}: No data",
            'method': f'retry_round_{round_num}_failed'
        }

async def fetch_smart_retry(exchange_name, symbol, semaphore):
    """Legacy retry function for backward compatibility"""
    return await fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, 1, 0.0)

async def smart_scan(token_exchange_map, max_concurrent=60, retry_failed=True, target_usd_amount=10.0):
    """Smart scan with liquidity analysis - only request tokens that exist on each exchange"""
    print(f"\n🧠 SMART SCAN: {max_concurrent} concurrent requests")
    print("🎯 Only requesting tokens that exist on each exchange")
    print(f"💰 Target trade amount: ${target_usd_amount}")
    if retry_failed:
        print("🔄 Will retry failed requests after initial scan")

    semaphore = asyncio.Semaphore(max_concurrent)
    tasks = []
    total_requests = 0
    request_info = []  # Store request info for retries

    # Only create requests for tokens that actually exist on each exchange
    blacklisted_count = 0
    for token, exchanges in token_exchange_map.items():
        symbol = f"{token}/USDT"
        for exchange_name in exchanges:
            # Double-check the token exists on this exchange and is not blacklisted
            if (exchange_name in exchange_markets and
                token in exchange_markets[exchange_name] and
                not is_token_blacklisted(exchange_name, token)):
                task = fetch_smart(exchange_name, symbol, semaphore, target_usd_amount)
                tasks.append(task)
                request_info.append((exchange_name, symbol))  # Store for retries
                total_requests += 1
            elif is_token_blacklisted(exchange_name, token):
                blacklisted_count += 1

    print(f"📊 Smart requests: {total_requests:,}")
    print(f"📈 Tokens: {len(token_exchange_map)}")
    print(f"🏢 Average exchanges per token: {total_requests/len(token_exchange_map):.1f}")
    if blacklisted_count > 0:
        print(f"🚫 Blacklisted requests filtered: {blacklisted_count}")

    start_time = time.time()

    # Execute with progress
    print("\n🔄 Initial scan progress: ", end="", flush=True)
    completed = 0
    results = []
    failed_requests = []  # Store failed requests for retry

    # Execute all tasks and collect results
    all_results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and identify failures
    for i, result in enumerate(all_results):
        completed += 1

        if isinstance(result, Exception):
            # Exception occurred
            error_result = {
                'exchange': request_info[i][0] if i < len(request_info) else 'unknown',
                'symbol': request_info[i][1] if i < len(request_info) else 'unknown',
                'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(result)[:50], 'method': 'exception'
            }
            results.append(error_result)
            if i < len(request_info):
                failed_requests.append(request_info[i])
        elif result is not None:
            results.append(result)
            # Check if this request failed and should be retried
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                if i < len(request_info):
                    failed_requests.append(request_info[i])

        if completed % max(1, total_requests // 20) == 0:
            progress = (completed / total_requests) * 100
            print(f"{progress:.0f}%", end=" ", flush=True)

    print()

    initial_successful = len([r for r in results if r.get('error') is None and r.get('bid') and r.get('ask')])

    print(f"📊 Initial scan: {initial_successful}/{total_requests} successful ({initial_successful/total_requests*100:.1f}%)")

    # Retry failed requests if enabled
    if retry_failed and failed_requests:
        print(f"\n🔄 Retrying {len(failed_requests)} failed requests...")
        retry_results = await retry_failed_requests_multi_round(failed_requests, max_concurrent // 2, max_rounds=1)

        # Replace failed results with retry results where successful
        retry_dict = {(r['exchange'], r['symbol']): r for r in retry_results
                     if r.get('error') is None and r.get('bid') and r.get('ask')}

        # Update results with successful retries
        for i, result in enumerate(results):
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                key = (result.get('exchange', 'unknown'), result.get('symbol', 'unknown'))
                if key in retry_dict:
                    results[i] = retry_dict[key]

        # Add any new successful results from retries
        for retry_result in retry_results:
            key = (retry_result['exchange'], retry_result['symbol'])
            if key in retry_dict and retry_result not in results:
                results.append(retry_result)

    end_time = time.time()
    duration = end_time - start_time

    # Analyze results
    successful = []
    method_counts = defaultdict(int)
    exchange_success = defaultdict(int)
    exchange_total = defaultdict(int)

    for result in results:
        method_counts[result.get('method', 'unknown')] += 1
        exchange_total[result['exchange']] += 1

        if result['error'] is None and result['bid'] and result['ask']:
            successful.append(result)
            exchange_success[result['exchange']] += 1

    success_rate = len(successful) / total_requests * 100

    print(f"✅ SMART scan completed in {duration:.2f} seconds")
    print(f"📊 Final Success: {len(successful):,}/{total_requests:,} ({success_rate:.1f}%)")

    if retry_failed and failed_requests:
        retry_improvement = len(successful) - initial_successful
        print(f"🔄 Retry improvement: +{retry_improvement} successful requests")
        print(f"📈 Success rate improvement: {initial_successful/total_requests*100:.1f}% → {success_rate:.1f}%")

    print(f"🚀 Speed: {total_requests/duration:.0f} requests/second")

    print(f"\n📈 Method breakdown:")
    for method, count in sorted(method_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {method}: {count:,}")

    print(f"\n🏢 Exchange performance:")
    for ex in sorted(exchange_success.keys()):
        if exchange_total[ex] > 0:
            rate = exchange_success[ex]/exchange_total[ex]*100
            print(f"   {ex}: {rate:.1f}% ({exchange_success[ex]}/{exchange_total[ex]})")

    return successful

def calculate_max_possible_amount_within_tolerance(orderbook_side, target_price, tolerance_percent=3.0):
    """
    Calculate the total USDT value and average price of all orders within tolerance_percent of target_price

    Args:
        orderbook_side: List of [price, amount] pairs from orderbook (bids or asks)
        target_price: The target price to calculate tolerance around
        tolerance_percent: Percentage tolerance (default 3.0%)

    Returns:
        dict with total_usd_value and average_price
    """
    if not orderbook_side or not target_price or len(orderbook_side) == 0:
        return {'total_usd_value': 0.0, 'average_price': 0.0}

    # Calculate price range (3% tolerance)
    tolerance_factor = tolerance_percent / 100.0
    min_price = target_price * (1 - tolerance_factor)
    max_price = target_price * (1 + tolerance_factor)

    total_usd_value = 0.0
    total_tokens = 0.0

    for price, amount in orderbook_side:
        if min_price <= price <= max_price:
            total_usd_value += price * amount
            total_tokens += amount

    # Calculate weighted average price
    average_price = (total_usd_value / total_tokens) if total_tokens > 0 else 0.0

    return {'total_usd_value': total_usd_value, 'average_price': average_price}

def find_smart_arbitrage(results, min_usd_amount=10.0, max_slippage_percent=3.0):
    """Find arbitrage with liquidity filtering and realistic volume requirements"""
    symbol_data = defaultdict(list)
    for result in results:
        symbol_data[result['symbol']].append(result)

    opportunities = []

    for symbol, exchange_data in symbol_data.items():
        if len(exchange_data) < 2:
            continue

        # Find best bid and ask with liquidity consideration
        best_bid = None
        best_ask = None
        best_bid_exchange = None
        best_ask_exchange = None
        best_bid_data = None
        best_ask_data = None

        for data in exchange_data:
            if data['bid'] and (best_bid is None or data['bid'] > best_bid):
                best_bid = data['bid']
                best_bid_exchange = data['exchange']
                best_bid_data = data

            if data['ask'] and (best_ask is None or data['ask'] < best_ask):
                best_ask = data['ask']
                best_ask_exchange = data['exchange']
                best_ask_data = data

        if (best_bid and best_ask and best_bid > best_ask and
            best_bid_exchange != best_ask_exchange):

            profit_pct = ((best_bid - best_ask) / best_ask) * 100

            # Filter realistic opportunities (remove obvious data errors)
            if 0.01 <= profit_pct <= 15:  # Between 0.01% and 15%

                # Check liquidity requirements
                buy_liquidity_ok = True
                sell_liquidity_ok = True
                buy_slippage = 0
                sell_slippage = 0
                max_tradeable_usd = min_usd_amount

                # Check buy side liquidity (ask side)
                if best_ask_data and best_ask_data.get('ask_liquidity'):
                    ask_liq = best_ask_data['ask_liquidity']
                    if (ask_liq['achievable_usd'] < min_usd_amount or
                        ask_liq['slippage_percent'] > max_slippage_percent):
                        buy_liquidity_ok = False
                    else:
                        buy_slippage = ask_liq['slippage_percent']
                        max_tradeable_usd = min(max_tradeable_usd, ask_liq['max_usd_amount'])

                # Check sell side liquidity (bid side)
                if best_bid_data and best_bid_data.get('bid_liquidity'):
                    bid_liq = best_bid_data['bid_liquidity']
                    if (bid_liq['achievable_usd'] < min_usd_amount or
                        bid_liq['slippage_percent'] > max_slippage_percent):
                        sell_liquidity_ok = False
                    else:
                        sell_slippage = bid_liq['slippage_percent']
                        max_tradeable_usd = min(max_tradeable_usd, bid_liq['max_usd_amount'])

                # Calculate max possible buy/sell amounts within 3% tolerance
                max_possible_buy_amount = 0.0
                max_possible_sell_amount = 0.0
                avg_buy_price_3pct = 0.0
                avg_sell_price_3pct = 0.0

                # Calculate max possible buy amount (from ask orderbook within 3% of buy price)
                if best_ask_data and best_ask_data.get('orderbook_asks'):
                    buy_result = calculate_max_possible_amount_within_tolerance(
                        best_ask_data['orderbook_asks'], best_ask, 3.0
                    )
                    max_possible_buy_amount = buy_result['total_usd_value']
                    avg_buy_price_3pct = buy_result['average_price']

                # Calculate max possible sell amount (from bid orderbook within 3% of sell price)
                if best_bid_data and best_bid_data.get('orderbook_bids'):
                    sell_result = calculate_max_possible_amount_within_tolerance(
                        best_bid_data['orderbook_bids'], best_bid, 3.0
                    )
                    max_possible_sell_amount = sell_result['total_usd_value']
                    avg_sell_price_3pct = sell_result['average_price']

                # Only include opportunities with sufficient liquidity
                if buy_liquidity_ok and sell_liquidity_ok:
                    opportunity = {
                        'symbol': symbol,
                        'buy_exchange': best_ask_exchange,
                        'sell_exchange': best_bid_exchange,
                        'buy_price': best_ask,
                        'sell_price': best_bid,
                        'profit_percentage': profit_pct,
                        'profit_absolute': best_bid - best_ask,
                        'exchanges_count': len(exchange_data),
                        'buy_slippage': buy_slippage,
                        'sell_slippage': sell_slippage,
                        'max_tradeable_usd': max_tradeable_usd,
                        'max_possible_buy_amount': max_possible_buy_amount,
                        'max_possible_sell_amount': max_possible_sell_amount,
                        'avg_buy_price_3pct': avg_buy_price_3pct,
                        'avg_sell_price_3pct': avg_sell_price_3pct,
                        'liquidity_validated': True
                    }

                    # Add top prices for comparison
                    if best_ask_data and best_ask_data.get('top_ask'):
                        opportunity['top_buy_price'] = best_ask_data['top_ask']
                    if best_bid_data and best_bid_data.get('top_bid'):
                        opportunity['top_sell_price'] = best_bid_data['top_bid']

                    opportunities.append(opportunity)
                else:
                    # Still include but mark as insufficient liquidity
                    opportunities.append({
                        'symbol': symbol,
                        'buy_exchange': best_ask_exchange,
                        'sell_exchange': best_bid_exchange,
                        'buy_price': best_ask,
                        'sell_price': best_bid,
                        'profit_percentage': profit_pct,
                        'profit_absolute': best_bid - best_ask,
                        'exchanges_count': len(exchange_data),
                        'buy_slippage': buy_slippage,
                        'sell_slippage': sell_slippage,
                        'max_tradeable_usd': 0,
                        'max_possible_buy_amount': max_possible_buy_amount,
                        'max_possible_sell_amount': max_possible_sell_amount,
                        'avg_buy_price_3pct': avg_buy_price_3pct,
                        'avg_sell_price_3pct': avg_sell_price_3pct,
                        'liquidity_validated': False
                    })

    return sorted(opportunities, key=lambda x: (x['liquidity_validated'], x['profit_percentage']), reverse=True)

def format_price_with_precision(price, min_width=12):
    """Format price with appropriate precision for very small values"""
    if price is None or price == 0:
        return "N/A".ljust(min_width)

    # For very small prices, use more decimal places
    if price < 0.000001:  
        formatted = f"${price:.12}"
    elif price < 0.0001:  # Less than 0.0001
        formatted = f"${price:.8f}"
    elif price < 0.01:    # Less than 0.01
        formatted = f"${price:.6f}"
    else:                 # Regular prices
        formatted = f"${price:.4f}"

    return formatted.ljust(min_width)

def display_smart_results(opportunities, top_n=30):
    """Display smart results with liquidity information"""
    print(f"\n🎯 TOP {min(top_n, len(opportunities))} SMART ARBITRAGE OPPORTUNITIES")
    print("=" * 210)  # Increased width to accommodate longer price displays

    if not opportunities:
        print("No arbitrage opportunities found.")
        return

    print(f"{'#':<3} {'Symbol':<8} {'Buy@':<12} {'Sell@':<12} {'Buy Price':<14} {'Sell Price':<14} {'Profit%':<8} {'Profit$':<12} {'Liquidity':<10} {'Max USD':<8} {'Slippage':<10} {'Max Buy':<10} {'Max Sell':<10} {'Avg Buy':<14} {'Avg Sell':<14}")
    print("-" * 210)

    for i, opp in enumerate(opportunities[:top_n], 1):
        liquidity_status = "✅ Valid" if opp.get('liquidity_validated', False) else "❌ Low"
        max_usd = f"${opp.get('max_tradeable_usd', 0):.0f}" if opp.get('max_tradeable_usd', 0) > 0 else "N/A"

        buy_slip = opp.get('buy_slippage', 0)
        sell_slip = opp.get('sell_slippage', 0)
        slippage_str = f"{buy_slip:.2f}%/{sell_slip:.2f}%"

        # Format the new metrics with improved precision
        max_buy_amount = f"${opp.get('max_possible_buy_amount', 0):.0f}"
        max_sell_amount = f"${opp.get('max_possible_sell_amount', 0):.0f}"
        avg_buy_price = format_price_with_precision(opp.get('avg_buy_price_3pct', 0), 14) if opp.get('avg_buy_price_3pct', 0) > 0 else "N/A".ljust(14)
        avg_sell_price = format_price_with_precision(opp.get('avg_sell_price_3pct', 0), 14) if opp.get('avg_sell_price_3pct', 0) > 0 else "N/A".ljust(14)

        # Format main buy/sell prices with improved precision
        buy_price_formatted = format_price_with_precision(opp['buy_price'], 14)
        sell_price_formatted = format_price_with_precision(opp['sell_price'], 14)
        profit_absolute_formatted = format_price_with_precision(opp['profit_absolute'], 12)

        print(f"{i:<3} {opp['symbol']:<8} {opp['buy_exchange']:<12} {opp['sell_exchange']:<12} "
              f"{buy_price_formatted} {sell_price_formatted} {opp['profit_percentage']:<7.3f}% "
              f"{profit_absolute_formatted} {liquidity_status:<10} {max_usd:<8} {slippage_str:<10} "
              f"{max_buy_amount:<10} {max_sell_amount:<10} {avg_buy_price} {avg_sell_price}")

    print("=" * 210)

    # Summary statistics
    valid_opportunities = [opp for opp in opportunities if opp.get('liquidity_validated', False)]
    print(f"\n📊 LIQUIDITY SUMMARY:")
    print(f"   Total opportunities found: {len(opportunities)}")
    print(f"   With sufficient liquidity: {len(valid_opportunities)}")
    print(f"   Liquidity validation rate: {len(valid_opportunities)/len(opportunities)*100:.1f}%" if opportunities else "   No opportunities found")

    if valid_opportunities:
        avg_profit = sum(opp['profit_percentage'] for opp in valid_opportunities) / len(valid_opportunities)
        max_profit = max(opp['profit_percentage'] for opp in valid_opportunities)
        avg_tradeable = sum(opp.get('max_tradeable_usd', 0) for opp in valid_opportunities) / len(valid_opportunities)

        print(f"   Average profit (liquid): {avg_profit:.3f}%")
        print(f"   Maximum profit (liquid): {max_profit:.3f}%")
        print(f"   Average tradeable amount: ${avg_tradeable:.0f}")

        # Show slippage statistics
        buy_slippages = [opp.get('buy_slippage', 0) for opp in valid_opportunities]
        sell_slippages = [opp.get('sell_slippage', 0) for opp in valid_opportunities]
        if buy_slippages:
            print(f"   Average buy slippage: {sum(buy_slippages)/len(buy_slippages):.3f}%")
            print(f"   Average sell slippage: {sum(sell_slippages)/len(sell_slippages):.3f}%")

        # Show new metrics statistics
        max_buy_amounts = [opp.get('max_possible_buy_amount', 0) for opp in valid_opportunities]
        max_sell_amounts = [opp.get('max_possible_sell_amount', 0) for opp in valid_opportunities]
        avg_buy_prices = [opp.get('avg_buy_price_3pct', 0) for opp in valid_opportunities if opp.get('avg_buy_price_3pct', 0) > 0]
        avg_sell_prices = [opp.get('avg_sell_price_3pct', 0) for opp in valid_opportunities if opp.get('avg_sell_price_3pct', 0) > 0]

        if max_buy_amounts:
            avg_max_buy = sum(max_buy_amounts) / len(max_buy_amounts)
            avg_max_sell = sum(max_sell_amounts) / len(max_sell_amounts)
            print(f"   Average max buy amount (3% tolerance): ${avg_max_buy:.0f}")
            print(f"   Average max sell amount (3% tolerance): ${avg_max_sell:.0f}")

        if avg_buy_prices:
            avg_of_avg_buy = sum(avg_buy_prices) / len(avg_buy_prices)
            formatted_avg_buy = format_price_with_precision(avg_of_avg_buy, 0).strip()
            print(f"   Average buy price (3% tolerance): {formatted_avg_buy}")

        if avg_sell_prices:
            avg_of_avg_sell = sum(avg_sell_prices) / len(avg_sell_prices)
            formatted_avg_sell = format_price_with_precision(avg_of_avg_sell, 0).strip()
            print(f"   Average sell price (3% tolerance): {formatted_avg_sell}")

async def cleanup():
    """Close connections"""
    for exchange in exchange_pool.values():
        try:
            await exchange.close()
        except:
            pass

async def main():
    print("🧠 SMART ARBITRAGE SCANNER")
    print("=" * 50)
    print("✅ Only requests tokens that exist on each exchange")
    print("🎯 Dramatically improved success rates")
    print("⚡ Optimized for speed and accuracy")
    print("📊 Smart token-exchange mapping")
    print()

    try:
        # Show available exchanges for debugging
        # show_all_ccxt_exchanges()  # Uncomment to see all available exchanges

        # Smart initialization with market data
        token_exchange_map = await init_smart_exchanges()

        if not token_exchange_map:
            print("❌ No tokens found")
            return

        # Calculate smart requests
        total_smart_requests = sum(len(exchanges) for exchanges in token_exchange_map.values())
        print(f"📊 Smart requests: {total_smart_requests:,}")
        print(f"💡 vs Naive approach: {len(token_exchange_map) * len(exchange_pool):,} requests")
        reduction = (1 - total_smart_requests / (len(token_exchange_map) * len(exchange_pool))) * 100
        print(f"🎯 Request reduction: {reduction:.1f}%")

        # Smart scan with liquidity analysis
        target_amount = 10.0  # $10 target trade amount
        results = await smart_scan(token_exchange_map, max_concurrent=60, target_usd_amount=target_amount)

        if results:
            print(f"\n🎉 Got {len(results):,} successful results!")

            opportunities = find_smart_arbitrage(results, min_usd_amount=target_amount, max_slippage_percent=3.0)

            if opportunities:
                display_smart_results(opportunities, top_n=40)

                print(f"\n SMART SUMMARY:")
                print(f"   Exchanges used: {len(exchange_pool)}")
                print(f"   Tokens scanned: {len(token_exchange_map)}")
                print(f"   Successful requests: {len(results):,}")
                print(f"   Arbitrage opportunities: {len(opportunities)}")

                if opportunities:
                    profits = [o['profit_percentage'] for o in opportunities]
                    print(f"   Average profit: {sum(profits)/len(profits):.3f}%")
                    print(f"   Maximum profit: {max(profits):.3f}%")
                    print(f"   Median profit: {sorted(profits)[len(profits)//2]:.3f}%")

                    # Realistic profit ranges
                    very_high = len([p for p in profits if p > 5.0])
                    high = len([p for p in profits if 1.0 <= p <= 5.0])
                    medium = len([p for p in profits if 0.1 <= p < 1.0])
                    low = len([p for p in profits if p < 0.1])

                    print(f"   Very high profit (>5%): {very_high}")
                    print(f"   High profit (1-5%): {high}")
                    print(f"   Medium profit (0.1-1%): {medium}")
                    print(f"   Low profit (<0.1%): {low}")
            else:
                print("No arbitrage opportunities found")
        else:
            print("❌ No successful requests")

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await cleanup()

if __name__ == "__main__":
    asyncio.run(main())
